import React from 'react';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { PointsPackage } from '@/lib/services/points-system';

interface PointsPackageCardProps {
  pkg: PointsPackage;
  onPurchase: (pkg: PointsPackage) => void;
  isPopular?: boolean;
}

export function PointsPackageCard({ pkg, onPurchase, isPopular = false }: PointsPackageCardProps) {
  return (
    <Card className={`w-full transition-all duration-300 ${isPopular ? 'border-primary shadow-lg scale-105' : ''}`}>
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle className="text-xl">{pkg.name}</CardTitle>
          {isPopular && <Badge className="bg-primary">最受欢迎</Badge>}
        </div>
        <CardDescription>
          获得积分并解锁更多功能
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-baseline">
            <span className="text-3xl font-bold">¥{pkg.price}</span>
            <span className="text-muted-foreground ml-2">一次性付款</span>
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center">
              <div className="w-4 h-4 rounded-full bg-primary mr-2 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round" className="text-white">
                  <polyline points="20 6 9 17 4 12"></polyline>
                </svg>
              </div>
              <span>{pkg.points} 积分</span>
            </div>
            
            {pkg.bonus > 0 && (
              <div className="flex items-center">
                <div className="w-4 h-4 rounded-full bg-primary mr-2 flex items-center justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round" className="text-white">
                    <polyline points="20 6 9 17 4 12"></polyline>
                  </svg>
                </div>
                <span className="font-medium text-primary">额外赠送 {pkg.bonus} 积分</span>
              </div>
            )}
            
            <div className="flex items-center">
              <div className="w-4 h-4 rounded-full bg-primary mr-2 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round" className="text-white">
                  <polyline points="20 6 9 17 4 12"></polyline>
                </svg>
              </div>
              <span>解锁高级供应商信息</span>
            </div>
            
            <div className="flex items-center">
              <div className="w-4 h-4 rounded-full bg-primary mr-2 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round" className="text-white">
                  <polyline points="20 6 9 17 4 12"></polyline>
                </svg>
              </div>
              <span>查看联系方式</span>
            </div>
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Button 
          onClick={() => onPurchase(pkg)} 
          className="w-full"
          variant={isPopular ? "default" : "outline"}
        >
          立即购买
        </Button>
      </CardFooter>
    </Card>
  );
} 