import { db } from '../db';

export interface PointsPackage {
  id: string;
  name: string;
  points: number;
  price: number;
  bonus: number;
}

export const pointsPackages: PointsPackage[] = [
  {
    id: 'basic',
    name: '基础套餐',
    points: 100,
    price: 10,
    bonus: 0
  },
  {
    id: 'standard',
    name: '标准套餐',
    points: 500,
    price: 45,
    bonus: 50
  },
  {
    id: 'premium',
    name: '高级套餐',
    points: 1000,
    price: 80,
    bonus: 200
  },
  {
    id: 'enterprise',
    name: '企业套餐',
    points: 5000,
    price: 350,
    bonus: 1500
  }
];

export type PointsTransactionType = 
  | 'purchase' 
  | 'daily_checkin' 
  | 'content_unlock' 
  | 'contact_view' 
  | 'referral_bonus' 
  | 'admin_adjustment';

export interface PointsTransaction {
  id: string;
  userId: string;
  amount: number;
  type: PointsTransactionType;
  description: string;
  createdAt: Date;
  relatedEntityId?: string; // 产品ID、联系人ID等
}

export async function getUserPoints(userId: string): Promise<number> {
  try {
    // 获取用户所有积分交易
    const transactions = await db.pointsTransaction.findMany({
      where: { userId }
    });
    
    // 计算总积分
    return transactions.reduce((total, tx) => total + tx.amount, 0);
  } catch (error) {
    console.error('获取用户积分失败:', error);
    return 0;
  }
}

export async function addPointsTransaction(
  userId: string,
  amount: number,
  type: PointsTransactionType,
  description: string,
  relatedEntityId?: string
): Promise<boolean> {
  try {
    await db.pointsTransaction.create({
      data: {
        userId,
        amount,
        type,
        description,
        relatedEntityId,
        createdAt: new Date()
      }
    });
    return true;
  } catch (error) {
    console.error('添加积分交易失败:', error);
    return false;
  }
}

export async function canUserAffordAction(userId: string, requiredPoints: number): Promise<boolean> {
  const currentPoints = await getUserPoints(userId);
  return currentPoints >= requiredPoints;
}

export async function processPointsPackagePurchase(
  userId: string,
  packageId: string,
  paymentId: string
): Promise<boolean> {
  const pkg = pointsPackages.find(p => p.id === packageId);
  if (!pkg) return false;
  
  try {
    // 添加基础积分
    await addPointsTransaction(
      userId,
      pkg.points,
      'purchase',
      `购买${pkg.name}`,
      paymentId
    );
    
    // 添加奖励积分
    if (pkg.bonus > 0) {
      await addPointsTransaction(
        userId,
        pkg.bonus,
        'purchase',
        `${pkg.name}奖励积分`,
        paymentId
      );
    }
    
    return true;
  } catch (error) {
    console.error('处理积分包购买失败:', error);
    return false;
  }
}

export async function processContentUnlock(
  userId: string,
  contentId: string,
  contentType: string,
  pointsCost: number
): Promise<boolean> {
  if (!(await canUserAffordAction(userId, pointsCost))) {
    return false;
  }
  
  try {
    await addPointsTransaction(
      userId,
      -pointsCost,
      'content_unlock',
      `解锁${contentType}内容`,
      contentId
    );
    
    return true;
  } catch (error) {
    console.error('处理内容解锁失败:', error);
    return false;
  }
}

export async function processContactView(
  userId: string,
  contactId: string,
  pointsCost: number
): Promise<boolean> {
  if (!(await canUserAffordAction(userId, pointsCost))) {
    return false;
  }
  
  try {
    await addPointsTransaction(
      userId,
      -pointsCost,
      'contact_view',
      '查看联系方式',
      contactId
    );
    
    return true;
  } catch (error) {
    console.error('处理联系方式查看失败:', error);
    return false;
  }
}

export async function processDailyCheckin(userId: string): Promise<{success: boolean, points: number}> {
  try {
    // 检查今天是否已签到
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const existingCheckin = await db.pointsTransaction.findFirst({
      where: {
        userId,
        type: 'daily_checkin',
        createdAt: {
          gte: today
        }
      }
    });
    
    if (existingCheckin) {
      return { success: false, points: 0 };
    }
    
    // 检查连续签到天数
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    const yesterdayCheckin = await db.pointsTransaction.findFirst({
      where: {
        userId,
        type: 'daily_checkin',
        createdAt: {
          gte: yesterday,
          lt: today
        }
      }
    });
    
    // 基础积分 + 连续签到奖励
    const basePoints = 5;
    const consecutiveBonus = yesterdayCheckin ? 3 : 0;
    const totalPoints = basePoints + consecutiveBonus;
    
    await addPointsTransaction(
      userId,
      totalPoints,
      'daily_checkin',
      `每日签到 ${consecutiveBonus ? '(连续签到奖励)' : ''}`,
    );
    
    return { success: true, points: totalPoints };
  } catch (error) {
    console.error('处理每日签到失败:', error);
    return { success: false, points: 0 };
  }
} 