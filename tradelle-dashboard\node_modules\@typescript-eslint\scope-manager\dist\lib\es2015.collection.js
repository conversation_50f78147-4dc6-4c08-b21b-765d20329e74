"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib repo
Object.defineProperty(exports, "__esModule", { value: true });
exports.es2015_collection = void 0;
const base_config_1 = require("./base-config");
exports.es2015_collection = {
    libs: [],
    variables: [
        ['Map', base_config_1.TYPE_VALUE],
        ['MapConstructor', base_config_1.TYPE],
        ['ReadonlyMap', base_config_1.TYPE],
        ['WeakMap', base_config_1.TYPE_VALUE],
        ['WeakMapConstructor', base_config_1.TYPE],
        ['Set', base_config_1.TYPE_VALUE],
        ['SetConstructor', base_config_1.TYPE],
        ['ReadonlySet', base_config_1.TYPE],
        ['WeakSet', base_config_1.TYPE_VALUE],
        ['WeakSetConstructor', base_config_1.TYPE],
    ],
};
