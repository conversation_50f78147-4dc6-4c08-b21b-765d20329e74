{"version": 3, "file": "ConditionalTypeScope.d.ts", "sourceRoot": "", "sources": ["../../src/scope/ConditionalTypeScope.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,0BAA0B,CAAC;AAEzD,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAC;AACpD,OAAO,KAAK,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAErC,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AACxC,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AAExC,qBAAa,oBAAqB,SAAQ,SAAS,CACjD,SAAS,CAAC,eAAe,EACzB,QAAQ,CAAC,iBAAiB,EAC1B,KAAK,CACN;gBAEG,YAAY,EAAE,YAAY,EAC1B,UAAU,EAAE,oBAAoB,CAAC,OAAO,CAAC,EACzC,KAAK,EAAE,oBAAoB,CAAC,OAAO,CAAC;CAIvC"}